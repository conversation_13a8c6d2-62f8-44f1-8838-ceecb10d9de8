const express = require('express');
const router = express.Router();
const authMiddleware = require('../middleware/authMiddleware');
const {
  addActivityToBooking,
  addMultipleActivitiesToBooking,
  getBookingActivities,
  updateBookingActivity,
  deleteBookingActivity
} = require('../controllers/bookingActivityController');

// Tambah aktivitas ke booking
router.post('/:bookingId/activities', authMiddleware, addActivityToBooking);

// Tambah multiple aktivitas ke booking sekaligus
router.post('/booking_activities', authMiddleware, addMultipleActivitiesToBooking);

// Get semua aktivitas untuk booking tertentu
router.get('/:bookingId/activities', authMiddleware, getBookingActivities);

// Update aktivitas booking
router.put('/activities/:activityId', authMiddleware, updateBookingActivity);

// Hapus aktivitas booking
router.delete('/activities/:activityId', authMiddleware, deleteBookingActivity);

module.exports = router;
