# API Documentation - Booking System

## Overview
Sistem booking telah diperbaiki dengan fitur:
1. **Booking ID yang random namun teratur** dengan format: `BK-YYYYMMDD-XXXXX`
2. **Kode aktivitas berurutan** untuk setiap booking dengan format: `act001`, `act002`, dst.

## Booking Endpoints

### 1. Create Booking
**POST** `/booking`

Headers:
```
Authorization: Bearer <token>
Content-Type: application/json
```

Request Body:
```json
{
  "nama": "<PERSON>",
  "nomortlp": "081234567890",
  "tanggal_book": "2025-08-03 10:00:00 UTC",
  "hotel": "hotel_id_here",
  "transport": "Yes",
  "transport_type": "Medium Car",
  "nomor_driver": ""
}
```

Response:
```json
{
  "message": "Booking berhasil dibuat",
  "data": {
    "id": "***********-A1B2C",
    "nama": "<PERSON>",
    "nomortlp": "081234567890",
    "tanggal_book": "2025-08-03 10:00:00 UTC",
    "hotel": "hotel_id_here",
    "transport": "Yes",
    "transport_type": "Medium Car",
    "user_id": "user_id_here",
    "created": "2025-08-03 12:00:00.000Z",
    "updated": "2025-08-03 12:00:00.000Z"
  }
}
```

### 2. Get User Bookings
**GET** `/booking`

Headers:
```
Authorization: Bearer <token>
```

Response:
```json
{
  "message": "Data booking berhasil diambil",
  "data": [
    {
      "id": "***********-A1B2C",
      "nama": "John Doe",
      "nomortlp": "081234567890",
      "tanggal_book": "2025-08-03 10:00:00 UTC",
      "hotel": "hotel_id_here",
      "transport": "Yes",
      "transport_type": "Medium Car",
      "user_id": "user_id_here",
      "created": "2025-08-03 12:00:00.000Z",
      "updated": "2025-08-03 12:00:00.000Z",
      "expand": {
        "hotel": {
          "id": "hotel_id_here",
          "nama": "Hotel Gunadarma"
        }
      }
    }
  ]
}
```

### 3. Get Booking by ID
**GET** `/booking/:bookingId`

Headers:
```
Authorization: Bearer <token>
```

Response:
```json
{
  "message": "Detail booking berhasil diambil",
  "data": {
    "id": "***********-A1B2C",
    "nama": "John Doe",
    "nomortlp": "081234567890",
    "tanggal_book": "2025-08-03 10:00:00 UTC",
    "hotel": "hotel_id_here",
    "transport": "Yes",
    "transport_type": "Medium Car",
    "user_id": "user_id_here",
    "created": "2025-08-03 12:00:00.000Z",
    "updated": "2025-08-03 12:00:00.000Z",
    "expand": {
      "hotel": {
        "id": "hotel_id_here",
        "nama": "Hotel Gunadarma"
      }
    }
  }
}
```

## Booking Activities Endpoints

### 1. Add Activity to Booking
**POST** `/booking/:bookingId/activities`

Headers:
```
Authorization: Bearer <token>
Content-Type: application/json
```

Request Body:
```json
{
  "activity": "product_id_here",
  "qty": 2
}
```

Response:
```json
{
  "message": "Aktivitas berhasil ditambahkan ke booking",
  "data": {
    "id": "activity_record_id",
    "code_activity": "act001",
    "booking_id": "***********-A1B2C",
    "activity": "product_id_here",
    "qty": 2,
    "created": "2025-08-03 12:00:00.000Z",
    "updated": "2025-08-03 12:00:00.000Z"
  }
}
```

### 2. Get Booking Activities
**GET** `/booking/:bookingId/activities`

Headers:
```
Authorization: Bearer <token>
```

Response:
```json
{
  "message": "Data aktivitas booking berhasil diambil",
  "data": [
    {
      "id": "activity_record_id_1",
      "code_activity": "act001",
      "booking_id": "***********-A1B2C",
      "activity": "product_id_1",
      "qty": 2,
      "created": "2025-08-03 12:00:00.000Z",
      "updated": "2025-08-03 12:00:00.000Z",
      "expand": {
        "activity": {
          "id": "product_id_1",
          "nama_layanan": "Paket Wisata A"
        }
      }
    },
    {
      "id": "activity_record_id_2",
      "code_activity": "act002",
      "booking_id": "***********-A1B2C",
      "activity": "product_id_2",
      "qty": 1,
      "created": "2025-08-03 12:05:00.000Z",
      "updated": "2025-08-03 12:05:00.000Z",
      "expand": {
        "activity": {
          "id": "product_id_2",
          "nama_layanan": "Paket Wisata B"
        }
      }
    }
  ]
}
```

### 3. Update Booking Activity
**PUT** `/booking/activities/:activityId`

Headers:
```
Authorization: Bearer <token>
Content-Type: application/json
```

Request Body:
```json
{
  "activity": "new_product_id_here",
  "qty": 3
}
```

Response:
```json
{
  "message": "Aktivitas booking berhasil diupdate",
  "data": {
    "id": "activity_record_id",
    "code_activity": "act001",
    "booking_id": "***********-A1B2C",
    "activity": "new_product_id_here",
    "qty": 3,
    "created": "2025-08-03 12:00:00.000Z",
    "updated": "2025-08-03 12:10:00.000Z"
  }
}
```

### 4. Delete Booking Activity
**DELETE** `/booking/activities/:activityId`

Headers:
```
Authorization: Bearer <token>
```

Response:
```json
{
  "message": "Aktivitas booking berhasil dihapus"
}
```

## Error Responses

### 400 Bad Request
```json
{
  "message": "Activity dan qty harus diisi"
}
```

### 401 Unauthorized
```json
{
  "message": "Token tidak valid"
}
```

### 403 Forbidden
```json
{
  "message": "Anda tidak memiliki akses ke booking ini"
}
```

### 404 Not Found
```json
{
  "message": "Booking tidak ditemukan"
}
```

### 500 Internal Server Error
```json
{
  "message": "Gagal membuat booking",
  "error": "Error details here"
}
```

## Notes

1. **Booking ID Format**: `BK-YYYYMMDD-XXXXX` dimana XXXXX adalah 5 karakter random alphanumeric
2. **Activity Code Format**: `act001`, `act002`, `act003`, dst. berurutan untuk setiap booking
3. Semua endpoint memerlukan authentication token
4. User hanya bisa mengakses booking dan aktivitas milik mereka sendiri
5. Kode aktivitas akan otomatis di-generate berurutan untuk setiap booking
