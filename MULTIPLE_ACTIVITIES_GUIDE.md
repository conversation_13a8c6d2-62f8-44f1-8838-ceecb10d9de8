# Guide: Menambahkan Multiple Activities ke Booking

## Ma<PERSON><PERSON> yang Diselesaikan

Sebelumnya, untuk menambahkan beberapa aktivitas ke booking, Anda harus melakukan multiple request:

```
POST /booking/:bookingId/activities (untuk activity 1)
POST /booking/:bookingId/activities (untuk activity 2)  
POST /booking/:bookingId/activities (untuk activity 3)
```

Sekarang Anda bisa menambahkan semua aktivitas sekaligus dalam satu request.

## Endpoint Baru

**POST** `/booking_activities`

## Format Request

```json
{
  "booking_id": "booking_id_dari_pocketbase",
  "activity": [
    {
      "id": "product_id_1",
      "qty": 2
    },
    {
      "id": "product_id_2", 
      "qty": 3
    },
    {
      "id": "product_id_3",
      "qty": 1
    }
  ]
}
```

## Contoh Penggunaan

### 1. <PERSON>gan cURL

```bash
curl -X POST http://localhost:3000/booking_activities \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer YOUR_TOKEN" \
  -d '{
    "booking_id": "20250803+07vbixab",
    "activity": [
      {
        "id": "jya23u4o25swg5p",
        "qty": 2
      },
      {
        "id": "hh18pebys9a3mq2",
        "qty": 3
      },
      {
        "id": "h73wy96f970dz07",
        "qty": 1
      }
    ]
  }'
```

### 2. Dengan JavaScript/Fetch

```javascript
const response = await fetch('http://localhost:3000/booking_activities', {
  method: 'POST',
  headers: {
    'Content-Type': 'application/json',
    'Authorization': 'Bearer YOUR_TOKEN'
  },
  body: JSON.stringify({
    booking_id: "20250803+07vbixab",
    activity: [
      {
        id: "jya23u4o25swg5p",
        qty: 2
      },
      {
        id: "hh18pebys9a3mq2", 
        qty: 3
      },
      {
        id: "h73wy96f970dz07",
        qty: 1
      }
    ]
  })
});

const result = await response.json();
console.log(result);
```

## Response

```json
{
  "message": "3 aktivitas berhasil ditambahkan ke booking",
  "data": [
    {
      "id": "record_id_1",
      "code_activity": "act001",
      "booking_id": "20250803+07vbixab",
      "activity": "jya23u4o25swg5p",
      "qty": 2,
      "created": "2025-08-03 12:00:00.000Z",
      "updated": "2025-08-03 12:00:00.000Z"
    },
    {
      "id": "record_id_2", 
      "code_activity": "act002",
      "booking_id": "20250803+07vbixab",
      "activity": "hh18pebys9a3mq2",
      "qty": 3,
      "created": "2025-08-03 12:00:00.000Z",
      "updated": "2025-08-03 12:00:00.000Z"
    },
    {
      "id": "record_id_3",
      "code_activity": "act003", 
      "booking_id": "20250803+07vbixab",
      "activity": "h73wy96f970dz07",
      "qty": 1,
      "created": "2025-08-03 12:00:00.000Z",
      "updated": "2025-08-03 12:00:00.000Z"
    }
  ]
}
```

## Validasi

Endpoint ini akan memvalidasi:

1. **booking_id** harus ada dan valid
2. **activity** harus berupa array dan tidak kosong
3. Setiap activity harus memiliki **id** dan **qty**
4. **qty** harus lebih dari 0
5. Booking dengan booking_id tersebut harus ada di database

## Kode Aktivitas Otomatis

Sistem akan otomatis generate kode aktivitas berurutan:
- Activity pertama: `act001`
- Activity kedua: `act002` 
- Activity ketiga: `act003`
- Dan seterusnya...

## Error Handling

Jika ada error pada salah satu activity, seluruh proses akan dibatalkan dan tidak ada activity yang tersimpan.

## Catatan Penting

- Gunakan `booking_id` dari PocketBase (bukan booking_id yang di-generate dengan format BK-YYYYMMDD-XXXXX)
- Pastikan semua `activity.id` adalah ID yang valid dari collection `product_uab`
- Endpoint ini memerlukan authentication token
