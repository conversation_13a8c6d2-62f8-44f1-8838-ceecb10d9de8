const pb = require('../pocketbase/pbClient');
const { generateActivityCode, getNextActivitySequence } = require('../utils/idGenerator');

/**
 * Tambah aktivitas ke booking yang sudah ada
 */
exports.addActivityToBooking = async (req, res) => {
  try {
    const { bookingId } = req.params;
    const { activity, qty } = req.body;

    // Validasi input
    if (!activity || !qty) {
      return res.status(400).json({
        message: 'Activity dan QTY harus diisi'
      });
    }

    if (qty <= 0) {
      return res.status(400).json({
        message: 'Qty harus lebih dari 0'
      });
    }

    // Cek apakah booking exists
    try {
      await pb.collection('booking_uab').getOne(bookingId);
    } catch (error) {
      return res.status(404).json({
        message: 'Booking tidak ditemukan'
      });
    }

    // Get next sequence number untuk booking ini
    const nextSequence = await getNextActivitySequence(pb, bookingId);
    const activityCode = generateActivityCode(nextSequence);

    // Simpan booking activity
    const bookingActivity = await pb.collection('booking_activities').create({
      code_activity: activityCode,
      booking_id: bookingId,
      activity: activity,
      qty: qty
    });

    res.status(201).json({
      message: 'Aktivitas berhasil ditambahkan ke booking',
      data: bookingActivity
    });

  } catch (err) {
    console.error('Error adding activity to booking:', err);
    res.status(500).json({
      message: "Gagal menambahkan aktivitas ke booking",
      error: err?.response?.data || err.message || err
    });
  }
};

/**
 * Tambah multiple aktivitas ke booking sekaligus
 */
exports.addMultipleActivitiesToBooking = async (req, res) => {
  try {
    const { booking_id, activity } = req.body;

    // Validasi input
    if (!booking_id) {
      return res.status(400).json({
        message: 'booking_id harus diisi'
      });
    }

    if (!activity || !Array.isArray(activity) || activity.length === 0) {
      return res.status(400).json({
        message: 'activity harus berupa array dan tidak boleh kosong'
      });
    }

    // Validasi setiap activity
    for (let i = 0; i < activity.length; i++) {
      const act = activity[i];
      if (!act.id || !act.qty) {
        return res.status(400).json({
          message: `Activity index ${i}: id dan qty harus diisi`
        });
      }
      if (act.qty <= 0) {
        return res.status(400).json({
          message: `Activity index ${i}: qty harus lebih dari 0`
        });
      }
    }

    // Cek apakah booking exists
    try {
      await pb.collection('booking_uab').getFirstListItem(`booking_id = "${booking_id}"`);
    } catch (error) {
      return res.status(404).json({
        message: 'Booking tidak ditemukan'
      });
    }

    // Get next sequence number untuk booking ini
    let nextSequence = await getNextActivitySequence(pb, booking_id);

    // Simpan semua activities
    const savedActivities = [];
    for (const act of activity) {
      const activityCode = generateActivityCode(nextSequence);

      const bookingActivity = await pb.collection('booking_activities').create({
        code_activity: activityCode,
        booking_id: booking_id,
        activity: act.id,
        qty: act.qty
      });

      savedActivities.push(bookingActivity);
      nextSequence++;
    }

    res.status(201).json({
      message: `${savedActivities.length} aktivitas berhasil ditambahkan ke booking`,
      data: savedActivities
    });

  } catch (err) {
    console.error('Error adding multiple activities to booking:', err);
    res.status(500).json({
      message: "Gagal menambahkan aktivitas ke booking",
      error: err?.response?.data || err.message || err
    });
  }
};

/**
 * Get semua aktivitas untuk booking tertentu
 */
exports.getBookingActivities = async (req, res) => {
  try {
    const { bookingId } = req.params;

    // Cek apakah booking exists
    try {
      await pb.collection('booking_uab').getOne(bookingId);
    } catch (error) {
      return res.status(404).json({ 
        message: 'Booking tidak ditemukan' 
      });
    }

    // Get semua aktivitas untuk booking ini
    const activities = await pb.collection('booking_activities').getFullList({
      filter: `booking_id = "${bookingId}"`,
      sort: 'code_activity',
      expand: 'activity'
    });

    res.status(200).json({
      message: 'Data aktivitas booking berhasil diambil',
      data: activities
    });

  } catch (err) {
    console.error('Error getting booking activities:', err);
    res.status(500).json({
      message: "Gagal mengambil data aktivitas booking",
      error: err?.response?.data || err.message || err
    });
  }
};

/**
 * Update aktivitas booking
 */
exports.updateBookingActivity = async (req, res) => {
  try {
    const { activityId } = req.params;
    const { activity, qty } = req.body;

    // Validasi input
    if (qty && qty <= 0) {
      return res.status(400).json({ 
        message: 'Qty harus lebih dari 0' 
      });
    }

    // Update data
    const updateData = {};
    if (activity) updateData.activity = activity;
    if (qty) updateData.qty = qty;

    const updatedActivity = await pb.collection('booking_activities').update(activityId, updateData);

    res.status(200).json({
      message: 'Aktivitas booking berhasil diupdate',
      data: updatedActivity
    });

  } catch (err) {
    console.error('Error updating booking activity:', err);
    if (err.status === 404) {
      return res.status(404).json({ 
        message: 'Aktivitas booking tidak ditemukan' 
      });
    }
    res.status(500).json({
      message: "Gagal mengupdate aktivitas booking",
      error: err?.response?.data || err.message || err
    });
  }
};

/**
 * Hapus aktivitas booking
 */
exports.deleteBookingActivity = async (req, res) => {
  try {
    const { activityId } = req.params;

    await pb.collection('booking_activities').delete(activityId);

    res.status(200).json({
      message: 'Aktivitas booking berhasil dihapus'
    });

  } catch (err) {
    console.error('Error deleting booking activity:', err);
    if (err.status === 404) {
      return res.status(404).json({ 
        message: 'Aktivitas booking tidak ditemukan' 
      });
    }
    res.status(500).json({
      message: "Gagal menghapus aktivitas booking",
      error: err?.response?.data || err.message || err
    });
  }
};
